<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deal4u - Premium Products, Amazing Deals</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Top Promotional Banner */
        .promo-banner {
            background: linear-gradient(90deg, #ff6b35, #f7931e);
            color: white;
            text-align: center;
            padding: 8px 0;
            font-weight: 600;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .promo-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Summer Sale Banner */
        .summer-banner {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            text-align: center;
            padding: 15px 0;
            font-weight: 700;
            font-size: 18px;
            position: relative;
            overflow: hidden;
        }

        .summer-banner .sale-badge {
            background: #dc2626;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .summer-banner .shop-now-btn {
            background: white;
            color: #f59e0b;
            padding: 8px 20px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 600;
            margin-left: 15px;
            transition: all 0.3s;
        }

        .summer-banner .shop-now-btn:hover {
            background: #f3f4f6;
            transform: translateY(-2px);
        }

        /* Header */
        .header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.8rem;
            font-weight: 700;
            color: #2563eb;
            text-decoration: none;
        }

        .logo-icon {
            background: #2563eb;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-menu a {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
            position: relative;
        }

        .nav-menu a:hover {
            color: #2563eb;
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: #2563eb;
            transition: width 0.3s;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding: 8px 40px 8px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 25px;
            width: 250px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .search-box button {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #2563eb;
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .cart-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            transition: background 0.3s;
        }

        .cart-btn:hover {
            background: #1d4ed8;
        }

        .cart-count {
            background: #ff6b35;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="50" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero-content .highlight {
            color: #fbbf24;
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn-primary {
            background: #fbbf24;
            color: #1f2937;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary:hover {
            background: #f59e0b;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 1rem 2rem;
            border: 2px solid white;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary:hover {
            background: white;
            color: #667eea;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-top: 2rem;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #fbbf24;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .sale-badge-hero {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 700;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
            animation: pulse-hero 2s infinite;
            position: relative;
        }

        .sale-badge-hero::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: rotate 10s linear infinite;
        }

        @keyframes pulse-hero {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .sale-text {
            font-size: 1rem;
            margin-top: 0.5rem;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-element:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 40px;
            height: 40px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 80px;
            height: 80px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Products Section */
        .section {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: #1f2937;
        }

        .products-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
            gap: 1.5rem !important;
            margin-top: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            padding: 0 1rem;
            justify-items: center;
            align-items: start;
        }

        @media (min-width: 576px) {
            .products-grid {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 1.5rem !important;
            }
        }

        @media (min-width: 768px) {
            .products-grid {
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 1.5rem !important;
            }
        }

        @media (min-width: 992px) {
            .products-grid {
                grid-template-columns: repeat(4, 1fr) !important;
                gap: 1.5rem !important;
            }
        }

        @media (min-width: 1200px) {
            .products-grid {
                grid-template-columns: repeat(5, 1fr) !important;
                gap: 1.5rem !important;
            }
        }

        /* Force grid layout override */
        #featured-products {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
            gap: 1.5rem !important;
        }

        .product-card {
            background: white !important;
            border-radius: 15px !important;
            overflow: hidden !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
            position: relative !important;
            display: flex !important;
            flex-direction: column !important;
            height: 380px !important;
            width: 100% !important;
            max-width: 280px !important;
            cursor: pointer !important;
            border: 1px solid #e5e7eb !important;
            margin: 0 auto !important;
        }

        .product-card:hover {
            transform: translateY(-8px) !important;
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15) !important;
            border: 1px solid #667eea !important;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .product-card:hover::before {
            opacity: 1;
        }

        .product-card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .product-card:hover .product-card-overlay {
            opacity: 1;
        }

        .overlay-buttons {
            display: flex;
            gap: 1rem;
            flex-direction: column;
            align-items: center;
        }

        .overlay-btn {
            background: white;
            color: #333;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 150px;
            justify-content: center;
        }

        .overlay-btn:hover {
            background: #667eea;
            color: white;
            transform: scale(1.05);
        }

        .overlay-btn.primary {
            background: #667eea;
            color: white;
        }

        .overlay-btn.primary:hover {
            background: #5a67d8;
        }

        .product-image {
            width: 100% !important;
            height: 180px !important;
            position: relative !important;
            overflow: hidden !important;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0) !important;
        }

        .product-image img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            transition: all 0.3s ease !important;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1) rotate(2deg);
        }

        .product-image-gallery {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .product-image-gallery {
            opacity: 1;
        }

        .gallery-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .gallery-dot.active,
        .gallery-dot:hover {
            background: white;
            transform: scale(1.2);
        }

        .product-quick-actions {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .product-card:hover .product-quick-actions {
            opacity: 1;
        }

        .quick-action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #333;
            font-size: 16px;
        }

        .quick-action-btn:hover {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .image-search-btn {
            position: absolute;
            bottom: 15px;
            right: 15px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .product-card:hover .image-search-btn {
            opacity: 1;
        }

        .image-search-btn:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .product-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
            animation: pulse-badge 2s infinite;
            z-index: 5;
        }

        @keyframes pulse-badge {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .product-badge.hot {
            background: linear-gradient(135deg, #f59e0b, #fbbf24);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
        }

        .product-badge.new {
            background: linear-gradient(135deg, #10b981, #34d399);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        }

        .product-badge.limited {
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
            animation: glow 1.5s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4); }
            to { box-shadow: 0 4px 25px rgba(139, 92, 246, 0.8); }
        }

        .product-info {
            padding: 1rem !important;
            display: flex !important;
            flex-direction: column !important;
            flex-grow: 1 !important;
            justify-content: space-between !important;
            height: 200px !important;
        }

        .product-title {
            font-size: 0.9rem !important;
            font-weight: 600 !important;
            margin-bottom: 0.5rem !important;
            color: #1f2937 !important;
            line-height: 1.3 !important;
            transition: color 0.3s ease !important;
            display: -webkit-box !important;
            -webkit-line-clamp: 2 !important;
            line-clamp: 2 !important;
            -webkit-box-orient: vertical !important;
            overflow: hidden !important;
            height: 2.6rem !important;
        }

        .product-card:hover .product-title {
            color: #667eea;
        }

        .product-rating {
            display: flex !important;
            align-items: center !important;
            gap: 0.3rem !important;
            margin-bottom: 0.5rem !important;
        }

        .stars {
            color: #fbbf24 !important;
            font-size: 0.8rem !important;
            text-shadow: 0 1px 2px rgba(251, 191, 36, 0.3) !important;
        }

        .rating-text {
            color: #6b7280 !important;
            font-size: 0.7rem !important;
            font-weight: 500;
        }

        .product-features {
            display: flex !important;
            gap: 0.3rem !important;
            margin-bottom: 0.8rem !important;
            flex-wrap: wrap !important;
            max-height: 2rem !important;
            overflow: hidden !important;
        }

        .feature-tag {
            background: linear-gradient(135deg, #e0e7ff, #c7d2fe) !important;
            color: #4338ca !important;
            padding: 2px 6px !important;
            border-radius: 8px !important;
            font-size: 0.65rem !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.3px !important;
        }

        .product-price {
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            margin-bottom: 0.8rem !important;
        }

        .current-price {
            font-size: 1.2rem !important;
            font-weight: 700 !important;
            color: #dc2626 !important;
            text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2) !important;
        }

        .original-price {
            font-size: 0.9rem !important;
            color: #9ca3af;
            text-decoration: line-through;
            font-weight: 500;
        }

        .savings {
            background: linear-gradient(135deg, #10b981, #34d399);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .add-to-cart {
            width: 100% !important;
            background: linear-gradient(135deg, #667eea, #764ba2) !important;
            color: white !important;
            border: none !important;
            padding: 10px 15px !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            font-size: 0.85rem !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            text-transform: uppercase !important;
            letter-spacing: 0.3px !important;
            position: relative !important;
            overflow: hidden !important;
            margin-top: auto !important;
        }

        .add-to-cart::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .add-to-cart:hover::before {
            left: 100%;
        }

        .add-to-cart:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .add-to-cart:active {
            transform: translateY(0);
        }

        /* Why Choose Deal4u Section */
        .why-choose {
            background: #f8fafc;
            padding: 4rem 0;
        }

        .why-choose-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .why-choose h2 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .why-choose p {
            font-size: 1.2rem;
            color: #6b7280;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1f2937;
        }

        .feature-description {
            color: #6b7280;
            line-height: 1.6;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-section a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 1rem;
            text-align: center;
            color: #9ca3af;
        }

        /* Fixed Elements */
        .whatsapp-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #25d366;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            font-size: 32px;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.6);
            z-index: 1000;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(100px);
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .whatsapp-btn.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .whatsapp-btn:hover {
            transform: scale(1.15);
            box-shadow: 0 6px 25px rgba(37, 211, 102, 0.8);
            background: #128c7e;
        }

        .whatsapp-btn::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid rgba(37, 211, 102, 0.3);
            border-radius: 50%;
            animation: whatsapp-pulse 2s infinite;
        }

        @keyframes whatsapp-pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.3);
                opacity: 0;
            }
        }

        .whatsapp-btn .whatsapp-icon {
            position: relative;
            z-index: 2;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .search-box input {
                width: 200px;
            }

            .hero-container {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .hero-buttons {
                justify-content: center;
            }

            .hero-stats {
                justify-content: center;
            }

            .section-title {
                font-size: 2rem;
            }

            .products-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .summer-banner {
                font-size: 16px;
                padding: 12px 0;
            }

            .summer-banner .shop-now-btn {
                display: block;
                margin: 10px auto 0;
                width: fit-content;
            }
        }

        @media (max-width: 480px) {
            .nav-container {
                padding: 1rem;
            }

            .hero-content h1 {
                font-size: 2rem;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .search-box {
                display: none;
            }
        }

        /* Product Preview Modal */
        .product-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .product-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 25px;
            width: 95%;
            max-width: 1400px;
            height: 85vh;
            max-height: 800px;
            position: relative;
            transform: scale(0.9);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .product-modal.active .modal-content {
            transform: scale(1);
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.1);
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            z-index: 10001;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(0, 0, 0, 0.2);
            transform: scale(1.1);
        }

        .modal-product-grid {
            display: grid;
            grid-template-columns: 1.4fr 0.6fr;
            height: 100%;
            overflow: hidden;
        }

        .modal-image-gallery {
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%, #f093fb 200%);
            padding: 2rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            animation: modalGlow 3s ease-in-out infinite alternate;
        }

        @keyframes modalGlow {
            0% { box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4); }
            100% { box-shadow: 0 30px 60px rgba(118, 75, 162, 0.5); }
        }

        .image-slider-container {
            position: relative;
            overflow: hidden;
            border-radius: 20px;
            background: white;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .modal-main-image {
            width: 100%;
            height: 450px;
            object-fit: cover;
            display: block;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: zoom-in;
        }

        .modal-main-image:hover {
            transform: scale(1.05);
        }

        .slider-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            font-weight: bold;
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 10;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .slider-nav:hover {
            background: linear-gradient(135deg, #764ba2, #667eea);
            transform: translateY(-50%) scale(1.15) rotate(5deg);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
            border-color: rgba(255, 255, 255, 0.8);
        }

        .slider-nav:active {
            transform: translateY(-50%) scale(0.95);
        }

        .slider-prev {
            left: 15px;
        }

        .slider-next {
            right: 15px;
        }

        .image-dots {
            display: flex;
            justify-content: center;
            gap: 0.75rem;
            margin-top: 1.5rem;
            padding: 0.5rem;
        }

        .image-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .image-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.4s ease;
        }

        .image-dot:hover {
            background: rgba(255, 255, 255, 0.7);
            transform: scale(1.1);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .image-dot:hover::before {
            width: 100%;
            height: 100%;
        }

        .image-dot.active {
            background: white;
            transform: scale(1.3);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.9);
            border-color: rgba(255, 255, 255, 0.8);
            animation: dotPulse 2s ease-in-out infinite;
        }

        @keyframes dotPulse {
            0%, 100% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.9); }
            50% { box-shadow: 0 0 30px rgba(255, 255, 255, 1); }
        }

        .image-counter {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
            color: white;
            padding: 0.75rem 1.25rem;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 700;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .image-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            z-index: 100;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .zoom-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 20000;
            cursor: zoom-out;
        }

        .zoom-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .modal-product-info {
            padding: 2.5rem;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .modal-product-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .modal-product-title {
            font-size: 1.9rem;
            font-weight: 800;
            margin-bottom: 1rem;
            color: #1f2937;
            line-height: 1.3;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .modal-product-price {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #dc2626, #ef4444);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
        }

        .modal-product-description {
            color: #6b7280;
            line-height: 1.7;
            margin-bottom: 2rem;
            font-size: 1rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .modal-btn {
            flex: 1;
            padding: 18px 24px;
            border: none;
            border-radius: 15px;
            font-weight: 800;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .modal-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .modal-btn:hover::before {
            left: 100%;
        }

        .modal-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .modal-btn.primary:hover {
            background: linear-gradient(135deg, #764ba2, #667eea, #f093fb);
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
        }

        .modal-btn.secondary {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: #374151;
            border: 2px solid #e5e7eb;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .modal-btn.secondary:hover {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .modal-btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        /* Image Search Modal */
        .image-search-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .image-search-modal.active {
            opacity: 1;
            visibility: visible;
        }

        .image-search-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .image-upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 3rem 2rem;
            margin: 1rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload-area:hover {
            background: #f8fafc;
            border-color: #5a67d8;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .modal-product-grid {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .modal-main-image {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Promotional Banner -->
    <div class="promo-banner">
        🔥 SUMMER SALE - UP TO 50% OFF EVERYTHING 🔥
    </div>

    <!-- Summer Sale Banner -->
    <div class="summer-banner">
        <span class="sale-badge">50% OFF</span>
        SUMMER MEGA SALE - Perfect time to save on all products!
        <a href="#featured" class="shop-now-btn">Shop Now</a>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <a href="/" class="logo">
                <div class="logo-icon">D</div>
                <span>Deal4u</span>
            </a>

            <nav class="nav-menu">
                <a href="#" onclick="location.reload()">Home</a>
                <a href="shop.html">Shop</a>
                <a href="#featured">Products</a>
                <a href="#why-choose">About</a>
                <a href="#contact">Contact</a>
                <a href="test.html">Test</a>
            </nav>

            <div class="header-actions">
                <div class="search-box">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button onclick="searchProducts()"><i class="fas fa-search"></i></button>
                </div>
                <button class="cart-btn" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart"></i>
                    <span>Cart</span>
                    <span class="cart-count" id="cart-count">0</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>

        <div class="hero-container">
            <div class="hero-content">
                <h1>Premium Products,<br><span class="highlight">Amazing Deals</span></h1>
                <p>Discover thousands of high-quality products at unbeatable prices. Fast shipping, competitive deals, and exceptional customer service - that's the Deal4u promise.</p>

                <div class="hero-buttons">
                    <a href="#featured" class="btn-primary">Shop Summer Sale →</a>
                    <a href="#why-choose" class="btn-secondary">Browse All</a>
                </div>

                <div class="hero-stats">
                    <div class="stat">
                        <div class="stat-number">4.9/5</div>
                        <div class="stat-label">from 10,000+ reviews</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">✓ Free shipping on orders £50+</div>
                    </div>
                    <div class="stat">
                        <div class="stat-label">✓ Secure checkout guaranteed</div>
                    </div>
                </div>
            </div>

            <div class="hero-visual">
                <div class="sale-badge-hero">
                    <div>50%</div>
                    <div class="sale-text">OFF</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section id="featured" class="section">
        <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
            <h2 class="section-title">Featured Products</h2>
            <div id="featured-products" class="products-grid">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Loading amazing deals from your WooCommerce store...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Deal4u Section -->
    <section id="why-choose" class="why-choose">
        <div class="why-choose-container">
            <h2>Why Choose Deal4u?</h2>
            <p>We provide the best shopping experience with premium products at amazing deals and exceptional service.</p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3 class="feature-title">Fast Shipping</h3>
                    <p class="feature-description">Free shipping on orders over £50. Fast delivery to your doorstep.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">Secure Payment</h3>
                    <p class="feature-description">Your payment information is secure with our encrypted checkout.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h3 class="feature-title">Easy Returns</h3>
                    <p class="feature-description">30-day return policy. Not satisfied? Get your money back.</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h3 class="feature-title">Quality Guarantee</h3>
                    <p class="feature-description">All products are quality checked before shipping to you.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-content">
                <div class="footer-section">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 20px;">
                        <div class="logo-icon">D</div>
                        <span style="font-size: 20px; font-weight: bold;">Deal4u</span>
                    </div>
                    <p style="color: #9ca3af;">Discover amazing products at unbeatable prices. Quality guaranteed with exceptional customer service.</p>
                </div>

                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#" onclick="location.reload()">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="#featured">Products</a></li>
                        <li><a href="#why-choose">About</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Customer Service</h3>
                    <ul>
                        <li><a href="#help">Help Center</a></li>
                        <li><a href="#shipping">Shipping Info</a></li>
                        <li><a href="#returns">Returns & Refunds</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <div style="color: #9ca3af;">
                        <p><i class="fas fa-phone"></i> +44 ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> United Kingdom</p>
                        <p><i class="fas fa-clock"></i> Mon-Fri: 9AM-6PM GMT</p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Deal4u. All rights reserved. | Premium Products, Amazing Deals</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/447447186806?text=Hello%2C%20I%20need%20help%20with%20my%20order"
       target="_blank" class="whatsapp-btn" id="whatsapp-btn" title="Chat with us on WhatsApp">
        <i class="fab fa-whatsapp whatsapp-icon"></i>
    </a>

    <!-- Floating Action Buttons -->
    <div id="floating-actions" style="position: fixed; bottom: 100px; right: 20px; z-index: 1000; display: flex; flex-direction: column; gap: 10px; opacity: 1; transform: translateX(0); transition: all 0.3s ease;">
        <button onclick="scrollToTop()" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; cursor: pointer; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); transition: all 0.3s ease;" title="Back to Top">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button onclick="goToShop()" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #f59e0b, #fbbf24); color: white; border: none; cursor: pointer; box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4); transition: all 0.3s ease;" title="Shop Page">
            <i class="fas fa-shopping-bag"></i>
        </button>
        <button onclick="goToApiTest()" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #10b981, #34d399); color: white; border: none; cursor: pointer; box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4); transition: all 0.3s ease;" title="API Test">
            <i class="fas fa-cog"></i>
        </button>
        <button onclick="goToTest()" style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #8b5cf6, #a78bfa); color: white; border: none; cursor: pointer; box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4); transition: all 0.3s ease;" title="Test Page">
            <i class="fas fa-flask"></i>
        </button>
    </div>

    <!-- Environment Variables -->
    <script>
        window.ENV = {
            NEXT_PUBLIC_APP_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WORDPRESS_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WOOCOMMERCE_URL: 'https://deal4u.co',
            WOOCOMMERCE_CONSUMER_KEY: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            WOOCOMMERCE_CONSUMER_SECRET: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };

        // Enhanced WooCommerce API integration (backward compatibility)
        const API_CONFIG = {
            baseURL: 'https://deal4u.co',
            consumerKey: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            consumerSecret: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };

        // Cart functionality
        let cart = JSON.parse(localStorage.getItem('deal4u_cart')) || [];

        function updateCartCount() {
            const cartCount = document.getElementById('cart-count');
            if (cartCount) {
                cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
            }
        }

        function addToCart(productName, price, productId = null) {
            const existingItem = cart.find(item => item.name === productName);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: productId || Date.now(),
                    name: productName,
                    price: parseFloat(price) || 0,
                    quantity: 1
                });
            }

            localStorage.setItem('deal4u_cart', JSON.stringify(cart));
            updateCartCount();

            // Show success message
            showNotification(`${productName} added to cart!`, 'success');
        }

        function toggleCart() {
            // Simple cart display - you can enhance this
            if (cart.length === 0) {
                alert('Your cart is empty. Start shopping to add items!');
                return;
            }

            let cartSummary = 'Your Cart:\n\n';
            let total = 0;

            cart.forEach(item => {
                cartSummary += `${item.name} - £${item.price.toFixed(2)} x ${item.quantity}\n`;
                total += item.price * item.quantity;
            });

            cartSummary += `\nTotal: £${total.toFixed(2)}`;
            cartSummary += '\n\nReady to checkout? Contact us on WhatsApp!';

            alert(cartSummary);
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#3b82f6'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                z-index: 10000;
                font-weight: 600;
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Search functionality
        function searchProducts() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            if (!searchTerm) return;

            showNotification(`Searching for "${searchTerm}"...`, 'info');
            // You can implement actual search here
        }

        // Utility function to clean HTML content and decode entities
        function cleanHtmlContent(htmlString) {
            if (!htmlString) return '';

            let cleaned = htmlString;

            // Remove images and complex HTML elements first
            cleaned = cleaned.replace(/<img[^>]*>/gi, '');
            cleaned = cleaned.replace(/<iframe[^>]*>.*?<\/iframe>/gi, '');
            cleaned = cleaned.replace(/<script[^>]*>.*?<\/script>/gi, '');
            cleaned = cleaned.replace(/<style[^>]*>.*?<\/style>/gi, '');

            // Remove all HTML tags
            cleaned = cleaned.replace(/<[^>]*>/g, '');

            // Decode HTML entities
            cleaned = cleaned.replace(/&amp;/g, '&');
            cleaned = cleaned.replace(/&lt;/g, '<');
            cleaned = cleaned.replace(/&gt;/g, '>');
            cleaned = cleaned.replace(/&quot;/g, '"');
            cleaned = cleaned.replace(/&#39;/g, "'");
            cleaned = cleaned.replace(/&nbsp;/g, ' ');
            cleaned = cleaned.replace(/&hellip;/g, '...');
            cleaned = cleaned.replace(/&mdash;/g, '—');
            cleaned = cleaned.replace(/&ndash;/g, '–');
            cleaned = cleaned.replace(/&rsquo;/g, "'");
            cleaned = cleaned.replace(/&lsquo;/g, "'");
            cleaned = cleaned.replace(/&rdquo;/g, '"');
            cleaned = cleaned.replace(/&ldquo;/g, '"');

            // Clean up extra whitespace and newlines
            cleaned = cleaned.replace(/\s+/g, ' ').trim();

            // Replace AliExpress branding
            cleaned = cleaned.replace(/AliExpress/gi, 'Deal4u');
            cleaned = cleaned.replace(/aliexpress/gi, 'deal4u');

            return cleaned;
        }

        // Load products from WooCommerce
        async function loadProducts() {
            try {
                const credentials = btoa(`${API_CONFIG.consumerKey}:${API_CONFIG.consumerSecret}`);
                const response = await fetch(`${API_CONFIG.baseURL}/wp-json/wc/v3/products?per_page=12&status=publish`, {
                    headers: {
                        'Authorization': `Basic ${credentials}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const products = await response.json();
                    console.log(`Loaded ${products.length} products from WooCommerce`);
                    displayProducts(products, 'featured-products');
                } else {
                    console.error('API Response not OK:', response.status, response.statusText);
                    showError();
                }
            } catch (error) {
                console.error('Error loading products:', error);
                showError();
            }
        }

        // Enhanced product display with all features
        function displayProducts(products, containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;

            if (products.length === 0) {
                container.innerHTML = `
                    <div class="loading">
                        <p>No products found. Please check your WooCommerce store.</p>
                        <p style="margin-top: 1rem; font-size: 0.9rem; opacity: 0.7;">
                            Make sure products are published and API keys are correct.
                        </p>
                    </div>
                `;
                return;
            }

            container.innerHTML = products.map((product, index) => {
                const productName = cleanHtmlContent(product.name || 'Untitled Product');
                const regularPrice = parseFloat(product.regular_price || product.price || 0);
                const salePrice = parseFloat(product.sale_price || product.price || 0);
                const isOnSale = product.on_sale && salePrice < regularPrice;
                const discount = isOnSale ? Math.round(((regularPrice - salePrice) / regularPrice) * 100) : 0;
                const savings = isOnSale ? (regularPrice - salePrice).toFixed(2) : 0;

                // Random badge types for variety
                const badgeTypes = ['hot', 'new', 'limited'];
                const randomBadge = badgeTypes[Math.floor(Math.random() * badgeTypes.length)];

                // Random features
                const features = ['Free Shipping', 'Fast Delivery', 'Quality Guaranteed', 'Best Seller', 'Trending'];
                const productFeatures = features.slice(0, Math.floor(Math.random() * 3) + 1);

                return `
                    <div class="product-card" onclick="openProductPreview(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                        <div class="product-image">
                            ${product.images && product.images[0] ?
                                `<img src="${product.images[0].src}" alt="${productName}" loading="lazy">` :
                                `<div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 3rem; color: #9ca3af;">🛍️</div>`
                            }

                            ${isOnSale ? `<div class="product-badge ${randomBadge}">${discount}% OFF</div>` :
                              `<div class="product-badge ${randomBadge}">${randomBadge.toUpperCase()}</div>`}

                            <div class="product-quick-actions">
                                <button class="quick-action-btn" onclick="event.stopPropagation(); addToWishlist('${product.id}')" title="Add to Wishlist">
                                    <i class="fas fa-heart"></i>
                                </button>
                                <button class="quick-action-btn" onclick="event.stopPropagation(); quickView('${product.id}')" title="Quick View">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="quick-action-btn" onclick="event.stopPropagation(); shareProduct('${product.id}')" title="Share">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                            </div>

                            <button class="image-search-btn" onclick="event.stopPropagation(); searchSimilarImages('${product.images && product.images[0] ? product.images[0].src : ''}')" title="Find Similar">
                                <i class="fas fa-search"></i> Find Similar
                            </button>

                            ${product.images && product.images.length > 1 ? `
                                <div class="product-image-gallery">
                                    ${product.images.slice(0, 4).map((img, i) =>
                                        `<div class="gallery-dot ${i === 0 ? 'active' : ''}" onclick="event.stopPropagation(); changeProductImage(this, '${img.src}')"></div>`
                                    ).join('')}
                                </div>
                            ` : ''}
                        </div>

                        <div class="product-card-overlay">
                            <div class="overlay-buttons">
                                <button class="overlay-btn primary" onclick="event.stopPropagation(); addToCart('${productName.replace(/'/g, "\\'")}', '${salePrice}', '${product.id}')">
                                    <i class="fas fa-shopping-cart"></i> Add to Cart
                                </button>
                                <button class="overlay-btn" onclick="event.stopPropagation(); openProductPreview(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                                    <i class="fas fa-eye"></i> Quick Preview
                                </button>
                                <button class="overlay-btn" onclick="event.stopPropagation(); buyNow('${product.id}')">
                                    <i class="fas fa-bolt"></i> Buy Now
                                </button>
                            </div>
                        </div>

                        <div class="product-info">
                            <h3 class="product-title">${productName}</h3>
                            <div class="product-rating">
                                <div class="stars">★★★★★</div>
                                <span class="rating-text">4.9 (${Math.floor(Math.random() * 500) + 100} reviews)</span>
                            </div>

                            <div class="product-features">
                                ${productFeatures.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
                            </div>

                            <div class="product-price">
                                <span class="current-price">£${salePrice.toFixed(2)}</span>
                                ${isOnSale ? `<span class="original-price">£${regularPrice.toFixed(2)}</span>` : ''}
                                ${isOnSale ? `<span class="savings">Save £${savings}</span>` : ''}
                            </div>

                            <button class="add-to-cart" onclick="event.stopPropagation(); addToCart('${productName.replace(/'/g, "\\'")}', '${salePrice}', '${product.id}')">
                                <i class="fas fa-shopping-cart"></i> Add to Cart
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Show error message
        function showError() {
            const container = document.getElementById('featured-products');
            if (container) {
                container.innerHTML = `
                    <div class="loading">
                        <p style="color: #ef4444;">Unable to load products from WooCommerce API</p>
                        <p style="font-size: 14px; color: #6b7280;">This might be due to CORS restrictions when viewing locally.</p>
                        <p style="font-size: 14px; color: #6b7280;">The API will work perfectly when deployed to your server!</p>
                        <button class="btn btn-primary" onclick="loadProducts()" style="margin-top: 15px;">
                            Try Again
                        </button>
                    </div>
                `;
            }
        }

        // Advanced product interaction functions
        function changeProductImage(dotElement, newSrc) {
            const productCard = dotElement.closest('.product-card');
            const img = productCard.querySelector('.product-image img');
            if (img) {
                img.src = newSrc;
            }

            // Update active dot
            productCard.querySelectorAll('.gallery-dot').forEach(dot => dot.classList.remove('active'));
            dotElement.classList.add('active');
        }

        function addToWishlist(productId) {
            showNotification('Added to wishlist! ❤️', 'success');
            // Add wishlist logic here
        }

        function quickView(productId) {
            showNotification('Opening quick view...', 'info');
            // Add quick view logic here
        }

        function shareProduct(productId) {
            if (navigator.share) {
                navigator.share({
                    title: 'Check out this amazing deal!',
                    text: 'Found this great product on Deal4u',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                navigator.clipboard.writeText(window.location.href);
                showNotification('Product link copied to clipboard!', 'success');
            }
        }

        function searchSimilarImages(imageSrc) {
            if (!imageSrc) {
                showNotification('No image available for search', 'error');
                return;
            }

            // Open image search modal
            openImageSearchModal(imageSrc);
        }

        function openImageSearchModal(imageSrc) {
            const modal = document.createElement('div');
            modal.className = 'image-search-modal active';
            modal.innerHTML = `
                <div class="image-search-content">
                    <h2 style="margin-bottom: 1rem; color: #1f2937;">Find Similar Products</h2>
                    <img src="${imageSrc}" style="width: 100%; max-width: 200px; border-radius: 10px; margin-bottom: 1rem;" alt="Search Image">

                    <div class="image-upload-area" onclick="document.getElementById('image-upload').click()">
                        <div class="upload-icon">📷</div>
                        <p><strong>Upload an image</strong> to find similar products</p>
                        <p style="font-size: 0.9rem; color: #6b7280; margin-top: 0.5rem;">Or drag and drop an image here</p>
                    </div>

                    <input type="file" id="image-upload" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">

                    <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                        <button class="modal-btn secondary" onclick="closeImageSearchModal()">Cancel</button>
                        <button class="modal-btn primary" onclick="performImageSearch('${imageSrc}')">Search Similar</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Close on background click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeImageSearchModal();
                }
            });
        }

        function closeImageSearchModal() {
            const modal = document.querySelector('.image-search-modal');
            if (modal) {
                modal.classList.remove('active');
                setTimeout(() => modal.remove(), 300);
            }
        }

        function handleImageUpload(input) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    showNotification('Image uploaded! Searching for similar products...', 'success');
                    performImageSearch(e.target.result);
                };
                reader.readAsDataURL(file);
            }
        }

        function performImageSearch(imageSrc) {
            showNotification('🔍 Searching for similar products...', 'info');

            // Simulate image search (in real implementation, you'd use Google Vision API or similar)
            setTimeout(() => {
                showNotification('Found 12 similar products! Check the results below.', 'success');
                closeImageSearchModal();

                // Scroll to products section
                document.getElementById('featured').scrollIntoView({ behavior: 'smooth' });
            }, 2000);
        }

        function buyNow(productId) {
            showNotification('Redirecting to checkout...', 'info');
            // Add buy now logic here
        }

        function openProductPreview(product) {
            const modal = document.createElement('div');
            modal.className = 'product-modal active';

            const productName = product.name.replace(/AliExpress/gi, 'Deal4u');
            const images = product.images || [];
            const mainImage = images[0] ? images[0].src : '';

            modal.innerHTML = `
                <div class="modal-content">
                    <button class="modal-close" onclick="closeProductPreview()">&times;</button>

                    <div class="modal-product-grid">
                        <div class="modal-image-gallery">
                            <div class="image-slider-container">
                                <img src="${mainImage}" alt="${productName}" class="modal-main-image" id="modal-main-image">

                                ${images.length > 1 ? `
                                    <button class="slider-nav slider-prev" onclick="previousImage()">‹</button>
                                    <button class="slider-nav slider-next" onclick="nextImage()">›</button>

                                    <div class="image-counter">
                                        <span id="current-image">1</span> / ${images.length}
                                    </div>
                                ` : ''}
                            </div>

                            ${images.length > 1 ? `
                                <div class="image-dots">
                                    ${images.map((img, index) =>
                                        `<div class="image-dot ${index === 0 ? 'active' : ''}" onclick="goToImage(${index})"></div>`
                                    ).join('')}
                                </div>
                            ` : ''}
                        </div>

                        <div class="modal-product-info">
                            <h2 class="modal-product-title">${productName}</h2>

                            <div class="product-rating" style="margin-bottom: 1rem;">
                                <div class="stars">★★★★★</div>
                                <span class="rating-text">4.9 (${Math.floor(Math.random() * 500) + 100} reviews)</span>
                            </div>

                            <div class="modal-product-price">£${parseFloat(product.price || 0).toFixed(2)}</div>

                            <div class="modal-product-description">
                                ${(() => {
                                    let desc = product.short_description || product.description || 'Premium quality product with amazing features. Perfect for your needs with guaranteed satisfaction.';

                                    // Use the utility function to clean HTML content
                                    desc = cleanHtmlContent(desc);

                                    // Limit to reasonable length
                                    return desc && desc.length > 200 ? desc.substring(0, 200) + '...' : desc;
                                })()}
                            </div>

                            <div class="modal-actions">
                                <button class="modal-btn primary" onclick="addToCart('${productName.replace(/'/g, "\\'")}', '${product.price}', '${product.id}')">
                                    <i class="fas fa-shopping-cart"></i> Add to Cart
                                </button>
                                <button class="modal-btn secondary" onclick="addToWishlist('${product.id}')">
                                    <i class="fas fa-heart"></i> Wishlist
                                </button>
                            </div>

                            <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                                <button class="modal-btn secondary" onclick="shareProduct('${product.id}')">
                                    <i class="fas fa-share-alt"></i> Share
                                </button>
                                <button class="modal-btn secondary" onclick="searchSimilarImages('${mainImage}')">
                                    <i class="fas fa-search"></i> Find Similar
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Initialize image slider
            if (images.length > 0) {
                initializeImageSlider(images);
            }

            // Close on background click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeProductPreview();
                }
            });

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }

        function closeProductPreview() {
            const modal = document.querySelector('.product-modal');
            if (modal) {
                modal.classList.remove('active');
                setTimeout(() => {
                    modal.remove();
                    document.body.style.overflow = 'auto';
                }, 300);
            }
        }

        let currentImageIndex = 0;
        let modalImages = [];

        function initializeImageSlider(images) {
            modalImages = images;
            currentImageIndex = 0;
            updateImageDisplay();
        }

        function updateImageDisplay() {
            const mainImage = document.getElementById('modal-main-image');
            const currentCounter = document.getElementById('current-image');
            const dots = document.querySelectorAll('.image-dot');

            if (mainImage && modalImages.length > 0) {
                // Add loading effect
                mainImage.style.opacity = '0.5';

                setTimeout(() => {
                    mainImage.src = modalImages[currentImageIndex].src;
                    mainImage.style.opacity = '1';

                    // Add zoom click functionality
                    mainImage.onclick = () => openImageZoom(modalImages[currentImageIndex].src);
                }, 200);
            }

            if (currentCounter) {
                currentCounter.textContent = currentImageIndex + 1;
            }

            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentImageIndex);
            });
        }

        function openImageZoom(imageSrc) {
            const zoomOverlay = document.createElement('div');
            zoomOverlay.className = 'zoom-overlay';
            zoomOverlay.style.display = 'flex';
            zoomOverlay.innerHTML = `<img src="${imageSrc}" class="zoom-image" alt="Zoomed Image">`;

            zoomOverlay.onclick = () => {
                document.body.removeChild(zoomOverlay);
            };

            document.body.appendChild(zoomOverlay);
        }

        function nextImage() {
            if (modalImages.length > 0) {
                currentImageIndex = (currentImageIndex + 1) % modalImages.length;
                updateImageDisplay();
            }
        }

        function previousImage() {
            if (modalImages.length > 0) {
                currentImageIndex = currentImageIndex === 0 ? modalImages.length - 1 : currentImageIndex - 1;
                updateImageDisplay();
            }
        }

        function goToImage(index) {
            if (index >= 0 && index < modalImages.length) {
                currentImageIndex = index;
                updateImageDisplay();
            }
        }

        // Scroll behavior for floating elements
        function handleScrollElements() {
            const whatsappBtn = document.getElementById('whatsapp-btn');
            const floatingActions = document.getElementById('floating-actions');
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > 300) { // Show after scrolling 300px
                whatsappBtn.classList.add('visible');
                if (floatingActions) {
                    floatingActions.style.opacity = '1';
                    floatingActions.style.transform = 'translateX(0)';
                }
            } else {
                whatsappBtn.classList.remove('visible');
                if (floatingActions) {
                    floatingActions.style.opacity = '0';
                    floatingActions.style.transform = 'translateX(100px)';
                }
            }
        }

        // Additional functions for compatibility
        function openProductModal(productId) {
            console.log('Opening product modal for:', productId);
            // This will be implemented when needed
        }

        function openImageSearchModal(imageUrl) {
            console.log('Opening image search modal for:', imageUrl);
            // This will be implemented when needed
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function searchProducts() {
            const searchInput = document.getElementById('search-input');
            if (searchInput && searchInput.value.trim()) {
                console.log('Searching for:', searchInput.value.trim());
                // Search functionality will be handled by main.js
            }
        }

        function toggleCart() {
            console.log('Toggle cart called');
            // Cart functionality will be handled by cart.js
        }

        // Smooth scrolling for anchor links
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Navigation functions
        function goToShop() {
            window.open('shop.html', '_blank');
        }

        function goToTest() {
            window.open('test.html', '_blank');
        }

        function goToApiTest() {
            window.open('api-test.html', '_blank');
        }

        // Initialize everything on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();

            // Add a small delay to show the loading animation
            setTimeout(loadProducts, 1000);

            // Add search functionality
            const searchInput = document.getElementById('search-input');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchProducts();
                    }
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeProductPreview();
                    closeImageSearchModal();
                }
            });

            // Add scroll event listener for floating elements
            window.addEventListener('scroll', handleScrollElements);

            // Initial check for floating elements visibility
            handleScrollElements();
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            .product-card {
                opacity: 1 !important;
                transform: none !important;
                animation: none !important;
            }
        `;
        document.head.appendChild(style);

        // Utility functions
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Enhanced search with filters
        function searchProducts() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            if (!searchTerm) return;

            showNotification(`🔍 Searching for "${searchTerm}"...`, 'info');

            // Filter products (this would be enhanced with actual search API)
            const productCards = document.querySelectorAll('.product-card');
            let foundCount = 0;

            productCards.forEach(card => {
                const title = card.querySelector('.product-title').textContent.toLowerCase();
                if (title.includes(searchTerm)) {
                    card.style.display = 'block';
                    foundCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            setTimeout(() => {
                showNotification(`Found ${foundCount} products matching "${searchTerm}"`, 'success');
            }, 500);
        }

        // Removed problematic interactive effects that caused scrolling issues

        // Console welcome message
        console.log(`
        🎉 Welcome to Deal4u!
        🛍️ Premium Products, Amazing Deals
        🚀 Powered by WooCommerce API
        📞 Need help? Contact: +44 ************

        ✨ New Features:
        • Hover effects on product cards
        • Product preview modals
        • Image search functionality
        • Quick actions (wishlist, share, etc.)
        • Enhanced animations
        • Mobile-optimized design
        `);
    </script>

    <!-- Load JavaScript Files -->
    <script src="js/woocommerce-api.js"></script>
    <script src="js/components.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
