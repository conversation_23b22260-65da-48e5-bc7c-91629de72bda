<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - Deal4u</title>

    <!-- Favicon and app icons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated background particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sparkles" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="white" opacity="0.1"><animate attributeName="opacity" values="0.1;0.3;0.1" dur="3s" repeatCount="indefinite"/></circle><circle cx="15" cy="15" r="1" fill="white" opacity="0.1"><animate attributeName="opacity" values="0.1;0.3;0.1" dur="2s" repeatCount="indefinite"/></circle></pattern></defs><rect width="100" height="100" fill="url(%23sparkles)"/></svg>');
            pointer-events: none;
            z-index: 0;
        }

        .product-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            margin-top: 2rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }

        .product-images {
            position: relative;
        }

        .main-image {
            width: 100%;
            height: 500px;
            object-fit: cover;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            margin-bottom: 1rem;
            transition: all 0.4s ease;
            cursor: zoom-in;
            position: relative;
            overflow: hidden;
        }

        .main-image:hover {
            transform: scale(1.02);
            box-shadow: 0 30px 60px rgba(0,0,0,0.3);
        }

        .main-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .main-image:hover::before {
            left: 100%;
        }

        .image-thumbnails {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .thumbnail:hover,
        .thumbnail.active {
            opacity: 1;
            border-color: #667eea;
            transform: scale(1.05);
        }

        .product-info h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            animation: fadeInLeft 0.8s ease-out 0.2s both;
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .product-rating {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stars {
            color: #ffd700;
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }

        .rating-text {
            color: #666;
        }

        .product-price {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #e53e3e 0%, #ff6b6b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1.5rem;
            text-shadow: 0 4px 8px rgba(229, 62, 62, 0.3);
            animation: pulse 2s ease-in-out infinite;
            position: relative;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .product-price::after {
            content: '🔥';
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            animation: bounce 1s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% {
                transform: translateY(-50%) scale(1);
            }
            50% {
                transform: translateY(-60%) scale(1.2);
            }
        }

        /* Deal Alert Banner */
        .deal-alert-banner {
            background: linear-gradient(135deg, #e53e3e 0%, #ff6b6b 100%);
            color: white;
            text-align: center;
            padding: 1rem;
            position: relative;
            z-index: 2;
            overflow: hidden;
            animation: slideDown 0.8s ease-out;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .deal-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            font-weight: 700;
            font-size: 1.1rem;
            letter-spacing: 2px;
            animation: flash 2s ease-in-out infinite;
        }

        @keyframes flash {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.8;
            }
        }

        .deal-icon {
            font-size: 1.5rem;
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* Modal animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .original-price {
            font-size: 1.5rem;
            color: #999;
            text-decoration: line-through;
            margin-right: 1rem;
        }

        .savings {
            background: linear-gradient(135deg, #e53e3e 0%, #ff6b6b 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(229, 62, 62, 0.4);
            animation: glow 2s ease-in-out infinite alternate;
            position: relative;
            overflow: hidden;
        }

        @keyframes glow {
            from {
                box-shadow: 0 4px 15px rgba(229, 62, 62, 0.4);
            }
            to {
                box-shadow: 0 6px 25px rgba(229, 62, 62, 0.6);
            }
        }

        .savings::before {
            content: '💥';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
        }

        .product-description {
            background: linear-gradient(135deg, rgba(247, 250, 252, 0.9) 0%, rgba(237, 242, 247, 0.9) 100%);
            backdrop-filter: blur(10px);
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-left: 4px solid #667eea;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .product-description::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-description:hover::before {
            opacity: 1;
        }

        .product-description h3 {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .description-text img {
            display: none !important;
        }

        /* Size Selection Styles */
        .size-selection {
            margin-bottom: 2rem;
            animation: fadeInUp 0.8s ease-out 0.5s both;
        }

        .size-selection h3 {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .size-options {
            display: flex;
            flex-wrap: wrap;
            gap: 0.8rem;
            margin-bottom: 1rem;
        }

        .size-btn {
            width: 50px;
            height: 50px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .size-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .size-btn:hover::before {
            left: 100%;
        }

        .size-btn:hover {
            border-color: #667eea;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .size-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .size-guide {
            margin-top: 1rem;
        }

        .size-guide-btn {
            background: transparent;
            border: 1px solid #667eea;
            color: #667eea;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .size-guide-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        .product-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            flex: 1;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.5);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .btn-secondary {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }

        .product-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .feature-item {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.3);
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .feature-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-item:hover::before {
            opacity: 1;
        }

        .feature-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.8rem;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .feature-item:hover .feature-icon {
            transform: scale(1.2) rotate(5deg);
            text-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 1rem;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            transition: all 0.4s ease;
            color: #667eea;
            font-size: 1.2rem;
        }

        .back-button:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.15) rotate(-5deg);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
            font-size: 1.2rem;
            color: #666;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .product-container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .product-info h1 {
                font-size: 1.5rem;
            }
            
            .product-price {
                font-size: 2rem;
            }
            
            .product-actions {
                flex-direction: column;
            }
        }
    </style>

    <!-- Environment Variables -->
    <script>
        window.ENV = {
            NEXT_PUBLIC_APP_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WORDPRESS_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WOOCOMMERCE_URL: 'https://deal4u.co',
            WOOCOMMERCE_CONSUMER_KEY: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            WOOCOMMERCE_CONSUMER_SECRET: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };

        // Enhanced WooCommerce API integration
        const API_CONFIG = {
            baseURL: 'https://deal4u.co',
            consumerKey: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            consumerSecret: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };
    </script>
</head>
<body>
    <!-- Back Button -->
    <button class="back-button" onclick="goBack()">
        <i class="fas fa-arrow-left"></i>
    </button>

    <!-- Header -->
    <header id="header">
        <nav class="bg-white/90 backdrop-blur-md shadow-lg sticky top-0 z-40 border-b border-white/20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="index.html" class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-lg">D</span>
                            </div>
                            <span class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Deal4u</span>
                        </a>
                    </div>

                    <!-- Navigation Links -->
                    <div class="hidden md:flex items-center space-x-6">
                        <a href="index.html" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Home</a>
                        <a href="shop.html" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Shop</a>
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Categories</a>
                        <a href="#" class="text-gray-700 hover:text-blue-600 transition-colors font-medium">Contact</a>
                    </div>

                    <!-- Cart & User Actions -->
                    <div class="flex items-center space-x-4">
                        <!-- Cart Button -->
                        <button onclick="toggleCart()" class="relative p-2 text-gray-700 hover:text-blue-600 transition-colors">
                            <i class="fas fa-shopping-cart text-xl"></i>
                            <span class="cart-count absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- User Menu -->
                        <button class="p-2 text-gray-700 hover:text-blue-600 transition-colors">
                            <i class="fas fa-user text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Deal Alert Banner -->
    <div class="deal-alert-banner">
        <div class="deal-content">
            <span class="deal-icon">🔥</span>
            <span class="deal-text">LIMITED TIME OFFER</span>
            <span class="deal-icon">🔥</span>
        </div>
    </div>

    <!-- Product Container -->
    <div class="product-container">
        <div id="product-content">
            <div class="loading">
                <div class="spinner"></div>
                Loading product details...
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer id="footer" class="bg-gray-900 text-white py-8 relative z-1">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">D</span>
                        </div>
                        <span class="text-xl font-bold">Deal4u</span>
                    </div>
                    <p class="text-gray-400">
                        Discover amazing products at unbeatable prices. Quality guaranteed.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="shop.html" class="text-gray-400 hover:text-white transition-colors">Shop</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Categories</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                    <div class="space-y-2 text-gray-400">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-phone"></i>
                            <span>+44 ************</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-envelope"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 Deal4u. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/447447186806?text=Hello%2C%20I%20need%20help%20with%20this%20product" target="_blank" style="
        position: fixed;
        bottom: 20px;
        left: 20px;
        width: 60px;
        height: 60px;
        background: #25d366;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        text-decoration: none;
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
        transition: all 0.3s ease;
        z-index: 1000;
    " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Scripts -->
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/components.js"></script>

    <script>
        // Get product ID from URL parameters
        function getProductIdFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('id');
        }

        // Go back to previous page
        function goBack() {
            if (document.referrer && document.referrer.includes(window.location.hostname)) {
                window.history.back();
            } else {
                window.location.href = 'index.html';
            }
        }

        // Load single product from WooCommerce
        async function loadProduct(productId) {
            console.log('🔄 Loading product:', productId);

            try {
                const credentials = btoa(`${API_CONFIG.consumerKey}:${API_CONFIG.consumerSecret}`);
                const apiUrl = `${API_CONFIG.baseURL}/wp-json/wc/v3/products/${productId}`;

                console.log('🌐 Making API request to:', apiUrl);

                const response = await fetch(apiUrl, {
                    headers: {
                        'Authorization': `Basic ${credentials}`,
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const product = await response.json();
                    console.log('✅ Product loaded successfully:', product);
                    displayProduct(product);
                } else {
                    console.error('❌ Failed to load product:', response.status, response.statusText);
                    showError();
                }
            } catch (error) {
                console.error('💥 Error loading product:', error);
                showError();
            }
        }

        // Display product details
        function displayProduct(product) {
            const productContent = document.getElementById('product-content');

            // Get product images
            const images = product.images && product.images.length > 0
                ? product.images
                : [{ src: 'https://via.placeholder.com/500x500?text=No+Image', alt: 'No image available' }];

            // Generate image thumbnails
            const thumbnailsHTML = images.length > 1
                ? `<div class="image-thumbnails">
                    ${images.map((img, index) =>
                        `<img src="${img.src}" alt="${img.alt}" class="thumbnail ${index === 0 ? 'active' : ''}"
                             onclick="changeMainImage('${img.src}', this)">`
                    ).join('')}
                   </div>`
                : '';

            // Calculate savings
            const regularPrice = parseFloat(product.regular_price) || 0;
            const salePrice = parseFloat(product.sale_price) || parseFloat(product.price) || 0;
            const savings = regularPrice > salePrice ? regularPrice - salePrice : 0;
            const savingsPercent = regularPrice > 0 ? Math.round((savings / regularPrice) * 100) : 0;

            // Generate rating stars
            const rating = product.average_rating ? parseFloat(product.average_rating) : 4.5;
            const starsHTML = Array.from({length: 5}, (_, i) =>
                `<i class="fas fa-star${i < Math.floor(rating) ? '' : (i < rating ? '-half-alt' : ' text-gray-300')}"></i>`
            ).join('');

            productContent.innerHTML = `
                <div class="product-grid">
                    <div class="product-images">
                        <img src="${images[0].src}" alt="${images[0].alt}" class="main-image" id="mainImage">
                        ${thumbnailsHTML}
                    </div>

                    <div class="product-info">
                        <h1>${product.name}</h1>

                        <div class="product-rating">
                            <div class="stars">${starsHTML}</div>
                            <span class="rating-text">(${product.rating_count || 118} reviews)</span>
                        </div>

                        <div class="product-price">
                            ${product.sale_price && product.sale_price !== product.regular_price
                                ? `<span class="original-price">£${product.regular_price}</span>`
                                : ''}
                            £${product.price}
                            ${savingsPercent > 0 ? `<span class="savings">Save ${savingsPercent}%</span>` : ''}
                        </div>

                        <!-- Size Selection -->
                        <div class="size-selection">
                            <h3>Size</h3>
                            <div class="size-options">
                                <button class="size-btn" data-size="XS" onclick="selectSize('XS', this)">XS</button>
                                <button class="size-btn" data-size="S" onclick="selectSize('S', this)">S</button>
                                <button class="size-btn active" data-size="M" onclick="selectSize('M', this)">M</button>
                                <button class="size-btn" data-size="L" onclick="selectSize('L', this)">L</button>
                                <button class="size-btn" data-size="XL" onclick="selectSize('XL', this)">XL</button>
                                <button class="size-btn" data-size="XXL" onclick="selectSize('XXL', this)">XXL</button>
                            </div>
                            <div class="size-guide">
                                <button class="size-guide-btn" onclick="openSizeGuide()">
                                    <i class="fas fa-ruler"></i>
                                    Size Guide
                                </button>
                            </div>
                        </div>

                        <div class="product-description">
                            <h3>Description</h3>
                            <div class="description-text">${cleanProductDescription(product.description || product.short_description || 'Premium quality product with amazing features. Perfect for your needs with guaranteed satisfaction.')}</div>
                        </div>

                        <div class="product-actions">
                            <button class="btn btn-primary" onclick="addToCart(${product.id})">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <button class="btn btn-secondary" onclick="addToWishlist(${product.id})">
                                <i class="fas fa-heart"></i>
                                Wishlist
                            </button>
                        </div>

                        <div class="product-features">
                            <div class="feature-item">
                                <div class="feature-icon"><i class="fas fa-shipping-fast"></i></div>
                                <div>Free Shipping</div>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon"><i class="fas fa-undo"></i></div>
                                <div>30-Day Returns</div>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                                <div>Secure Payment</div>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon"><i class="fas fa-headset"></i></div>
                                <div>24/7 Support</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Update page title
            document.title = `${product.name} - Deal4u`;
        }

        // Change main image when thumbnail is clicked
        function changeMainImage(src, thumbnail) {
            document.getElementById('mainImage').src = src;

            // Update active thumbnail
            document.querySelectorAll('.thumbnail').forEach(thumb => thumb.classList.remove('active'));
            thumbnail.classList.add('active');
        }

        // Show error message
        function showError() {
            const productContent = document.getElementById('product-content');
            productContent.innerHTML = `
                <div style="text-align: center; padding: 3rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                    <h2>Product Not Found</h2>
                    <p>Sorry, we couldn't load this product. It may have been removed or doesn't exist.</p>
                    <button class="btn btn-primary" onclick="goBack()" style="margin-top: 1rem;">
                        <i class="fas fa-arrow-left"></i>
                        Go Back
                    </button>
                </div>
            `;
        }

        // Clean product description - remove images and unwanted content
        function cleanProductDescription(description) {
            if (!description) return 'Premium quality product with amazing features.';

            // Remove HTML img tags
            let cleaned = description.replace(/<img[^>]*>/gi, '');

            // Remove size chart images and tables if they contain image references
            cleaned = cleaned.replace(/<table[^>]*>[\s\S]*?<\/table>/gi, '');

            // Remove any remaining image references
            cleaned = cleaned.replace(/\[img[^\]]*\]/gi, '');
            cleaned = cleaned.replace(/!\[.*?\]\(.*?\)/gi, '');

            // Clean up HTML entities
            cleaned = cleaned.replace(/&nbsp;/g, ' ');
            cleaned = cleaned.replace(/&amp;/g, '&');
            cleaned = cleaned.replace(/&lt;/g, '<');
            cleaned = cleaned.replace(/&gt;/g, '>');

            // Remove extra whitespace
            cleaned = cleaned.replace(/\s+/g, ' ').trim();

            // If description is too short or empty, provide a default
            if (cleaned.length < 20) {
                cleaned = 'Premium quality product with excellent craftsmanship. Perfect for your needs with guaranteed satisfaction and fast shipping.';
            }

            return cleaned;
        }

        // Size selection functionality
        let selectedSize = 'M'; // Default size

        function selectSize(size, button) {
            selectedSize = size;

            // Remove active class from all size buttons
            document.querySelectorAll('.size-btn').forEach(btn => btn.classList.remove('active'));

            // Add active class to selected button
            button.classList.add('active');

            console.log('Selected size:', size);
            showNotification(`Size ${size} selected!`, 'success');
        }

        function openSizeGuide() {
            // Create size guide modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    padding: 2rem;
                    border-radius: 20px;
                    max-width: 500px;
                    width: 90%;
                    position: relative;
                    animation: slideIn 0.3s ease;
                ">
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        position: absolute;
                        top: 1rem;
                        right: 1rem;
                        background: none;
                        border: none;
                        font-size: 1.5rem;
                        cursor: pointer;
                        color: #666;
                    ">&times;</button>

                    <h3 style="color: #667eea; margin-bottom: 1rem;">Size Guide</h3>

                    <table style="width: 100%; border-collapse: collapse; margin-bottom: 1rem;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 0.5rem; border: 1px solid #dee2e6;">Size</th>
                                <th style="padding: 0.5rem; border: 1px solid #dee2e6;">Bust (cm)</th>
                                <th style="padding: 0.5rem; border: 1px solid #dee2e6;">Waist (cm)</th>
                                <th style="padding: 0.5rem; border: 1px solid #dee2e6;">Hip (cm)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td style="padding: 0.5rem; border: 1px solid #dee2e6;">XS</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">78-82</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">58-62</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">83-87</td></tr>
                            <tr><td style="padding: 0.5rem; border: 1px solid #dee2e6;">S</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">83-87</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">63-67</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">88-92</td></tr>
                            <tr><td style="padding: 0.5rem; border: 1px solid #dee2e6;">M</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">88-92</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">68-72</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">93-97</td></tr>
                            <tr><td style="padding: 0.5rem; border: 1px solid #dee2e6;">L</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">93-97</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">73-77</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">98-102</td></tr>
                            <tr><td style="padding: 0.5rem; border: 1px solid #dee2e6;">XL</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">98-102</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">78-82</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">103-107</td></tr>
                            <tr><td style="padding: 0.5rem; border: 1px solid #dee2e6;">XXL</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">103-107</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">83-87</td><td style="padding: 0.5rem; border: 1px solid #dee2e6;">108-112</td></tr>
                        </tbody>
                    </table>

                    <p style="color: #666; font-size: 0.9rem;">
                        <strong>How to measure:</strong><br>
                        • Bust: Measure around the fullest part<br>
                        • Waist: Measure around the narrowest part<br>
                        • Hip: Measure around the fullest part
                    </p>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // Add missing functions for compatibility
        function addToCart(productId) {
            console.log('Adding product to cart:', productId, 'Size:', selectedSize);
            // Add cart functionality here with selected size
            showNotification(`Added to cart! Size: ${selectedSize}`, 'success');
        }

        function addToWishlist(productId) {
            console.log('Adding product to wishlist:', productId);
            // Add wishlist functionality here
            showNotification('Added to wishlist!', 'success');
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                animation: slideIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const productId = getProductIdFromURL();

            if (productId) {
                loadProduct(productId);
            } else {
                showError();
            }

            // Load header and footer components
            if (typeof loadComponents === 'function') {
                loadComponents();
            }
        });
    </script>
</body>
</html>
