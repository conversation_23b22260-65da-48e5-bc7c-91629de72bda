<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop - Deal4u | Premium Products, Amazing Deals</title>

    <!-- Favicon and app icons -->
    <link rel="icon" href="/favicon.ico" sizes="any">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#3b82f6">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'poppins': ['Poppins', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }

        /* Beautiful Summer Theme Styles */
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 60vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: slideInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            animation: slideInUp 1s ease-out 0.2s both;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Product Grid Styles */
        .products-container {
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .product-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .product-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.4s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.1);
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
            line-height: 1.4;
        }

        .product-price {
            font-size: 1.3rem;
            font-weight: 700;
            color: #e53e3e;
            margin-bottom: 1rem;
        }

        .product-rating {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stars {
            color: #ffd700;
            margin-right: 0.5rem;
        }

        .add-to-cart-btn {
            width: 100%;
            padding: 0.8rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .add-to-cart-btn:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        /* Filter Buttons */
        .filter-container {
            text-align: center;
            margin-bottom: 3rem;
        }

        .filter-btn {
            background: white;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        /* Loading Animation */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
                padding: 0 1rem;
            }

            .products-container {
                padding: 2rem 1rem;
            }
        }

        /* WhatsApp Button */
        .whatsapp-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 60px;
            height: 60px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .whatsapp-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
        }

        /* Additional styles for components */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .price-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .badge-sale {
            background: linear-gradient(135deg, #e53e3e 0%, #ff6b6b 100%);
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            max-height: 400px;
            overflow-y: auto;
            z-index: 50;
        }

        .cart-icon {
            position: relative;
        }

        .cart-count {
            position: absolute;
            top: -4px;
            right: -4px;
            background: #ef4444;
            color: white;
            font-size: 0.75rem;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
    </style>

    <!-- Environment Variables -->
    <script>
        window.ENV = {
            NEXT_PUBLIC_APP_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WORDPRESS_URL: 'https://deal4u.co',
            NEXT_PUBLIC_WOOCOMMERCE_URL: 'https://deal4u.co',
            WOOCOMMERCE_CONSUMER_KEY: 'ck_8d7ea6d7ea7571cfa97cdee38f8c44d33e8ac193',
            WOOCOMMERCE_CONSUMER_SECRET: 'cs_9af653aac4ee74e65657300da0fe6bb15ccb13f3'
        };
    </script>
</head>
<body>
    <!-- Header -->
    <header id="header"></header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">🛍️ Shop Amazing Deals</h1>
            <p class="hero-subtitle">Discover premium products at unbeatable prices • Free shipping worldwide</p>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-container">
        <!-- Filter Buttons -->
        <div class="filter-container">
            <button class="filter-btn active" onclick="filterProducts('all')">All Products</button>
            <button class="filter-btn" onclick="filterProducts('women-clothes')">👗 Women Clothes</button>
            <button class="filter-btn" onclick="filterProducts('electronics')">📱 Electronics</button>
            <button class="filter-btn" onclick="filterProducts('accessories')">💎 Accessories</button>
        </div>

        <!-- Products Grid -->
        <div id="products-grid" class="products-grid">
            <!-- Loading Animation -->
            <div class="loading">
                <div class="spinner"></div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="footer"></footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/447447186806?text=Hello%2C%20I%20need%20help%20with%20my%20order" target="_blank" class="whatsapp-btn">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- Scripts -->
    <script src="js/woocommerce-api.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/components.js"></script>
    <script src="js/main.js"></script>

    <script>
        let allProducts = [];
        let filteredProducts = [];
        let currentFilter = 'all';

        // Filter products by category
        function filterProducts(category) {
            currentFilter = category;

            // Update active button
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (category === 'all') {
                filteredProducts = allProducts;
            } else {
                filteredProducts = allProducts.filter(product => {
                    const productCategory = product.categories?.[0]?.name?.toLowerCase() || '';
                    return productCategory.includes(category.replace('-', ' '));
                });
            }

            renderProducts();
        }

        // Create beautiful product card
        function createProductCard(product) {
            const imageUrl = product.images?.[0]?.src || 'https://via.placeholder.com/300x250?text=No+Image';
            const price = product.price || '0.00';
            const salePrice = product.sale_price;
            const regularPrice = product.regular_price;

            // Generate star rating
            const rating = Math.floor(Math.random() * 2) + 4; // 4-5 stars
            const stars = '★'.repeat(rating) + '☆'.repeat(5 - rating);

            return `
                <div class="product-card" onclick="viewProduct(${product.id})">
                    <img src="${imageUrl}" alt="${product.name}" class="product-image" loading="lazy">
                    <div class="product-info">
                        <h3 class="product-title">${product.name}</h3>
                        <div class="product-rating">
                            <span class="stars">${stars}</span>
                            <span>(${Math.floor(Math.random() * 100) + 50})</span>
                        </div>
                        <div class="product-price">
                            ${salePrice ? `<span style="text-decoration: line-through; color: #999; margin-right: 10px;">£${regularPrice}</span>` : ''}
                            £${price}
                            ${salePrice ? '<span style="color: #e53e3e; font-size: 0.9rem; margin-left: 10px;">SALE!</span>' : ''}
                        </div>
                        <button class="add-to-cart-btn" onclick="event.stopPropagation(); addToCart(${product.id})">
                            <i class="fas fa-shopping-cart"></i> Add to Cart
                        </button>
                    </div>
                </div>
            `;
        }

        // Render products
        function renderProducts() {
            const grid = document.getElementById('products-grid');

            if (filteredProducts.length === 0) {
                grid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 4rem;">
                        <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                        <h3 style="color: #666; margin-bottom: 1rem;">No products found</h3>
                        <p style="color: #999;">Try selecting a different category or check back later for new arrivals!</p>
                    </div>
                `;
                return;
            }

            grid.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
        }

        // Load products from WooCommerce
        async function loadProducts() {
            try {
                const response = await fetch(`${window.ENV.NEXT_PUBLIC_WOOCOMMERCE_URL}/wp-json/wc/v3/products?per_page=100&consumer_key=${window.ENV.WOOCOMMERCE_CONSUMER_KEY}&consumer_secret=${window.ENV.WOOCOMMERCE_CONSUMER_SECRET}`);

                if (response.ok) {
                    allProducts = await response.json();
                    filteredProducts = allProducts;
                    renderProducts();
                } else {
                    throw new Error('Failed to load products');
                }
            } catch (error) {
                console.error('Error loading products:', error);
                document.getElementById('products-grid').innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; padding: 4rem;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                        <h3 style="color: #666; margin-bottom: 1rem;">Unable to load products</h3>
                        <p style="color: #999;">Please check your internet connection and try again.</p>
                        <button onclick="loadProducts()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Try Again
                        </button>
                    </div>
                `;
            }
        }

        // View product details
        function viewProduct(productId) {
            window.location.href = `product.html?id=${productId}`;
        }

        // Add to cart
        function addToCart(productId) {
            const product = allProducts.find(p => p.id === productId);
            if (product) {
                // Add to cart logic here
                alert(`Added "${product.name}" to cart!`);
            }
        }

        // Missing functions for components
        function toggleCart() {
            // Cart functionality
            console.log('Toggle cart');
        }

        function handleSearch(event) {
            if (event.key === 'Enter') {
                const query = event.target.value;
                console.log('Search:', query);
                // Implement search functionality
            }
        }

        function showToast(message, type = 'info') {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' : 'bg-blue-500'
            }`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Components will auto-load from components.js

            // Load products after components are ready
            setTimeout(() => {
                loadProducts();
            }, 1500);
        });
    </script>
</body>
</html>
